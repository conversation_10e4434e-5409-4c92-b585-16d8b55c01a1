<?php if (!empty($tareas_organizadas)): ?>
	<?php foreach ($tareas_organizadas as $item): ?>
		<?php
		$tarea_padre = $item['tarea'];
		$tareas_hijas = $item['hijas'];
		$tiene_hijas = !empty($tareas_hijas);
		?>

		<!-- Tarea Padre -->
		<tr data-tarea-id="<?php echo $tarea_padre->getId(); ?>" class="tarea-padre">
			<td class="text-center">
				<?php
				$tarea = $tarea_padre; // Set for the include
				include __ROOT__ . '/views/admin/ltareas_acciones.php';
				?>
			</td>
			<td class="text-center"><?php echo htmlspecialchars((string)$tarea_padre->getId()); ?></td>
			<td><?php echo $tarea_padre->getNombreProyectoModulo() ? htmlspecialchars($tarea_padre->getNombreProyectoModulo()) : 'N/A'; ?></td>
			<td>
				<?php if ($tiene_hijas): ?>
					<button type="button" class="btn-toggle-hijas" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
						<i class="fa fa-chevron-down"></i>
					</button>
				<?php endif; ?>
				<?php echo htmlspecialchars($tarea_padre->getDescripcion() ?? 'N/A'); ?>
				<?php if ($tiene_hijas): ?>
					<span class="badge badge-hijo-count"><?php echo count($tareas_hijas); ?> subtarea<?php echo count($tareas_hijas) > 1 ? 's' : ''; ?></span>
				<?php endif; ?>
			</td>
			<td class="text-center"><span class="badge <?php echo $tarea_padre->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_padre->getNombreTareaEstado()); ?></span></td>
			<td class="text-center"><?php echo format_dateyyymmdd($tarea_padre->getFechaTerminacion()); ?></td>
		</tr>

		<!-- Tareas Hijas -->
		<?php if ($tiene_hijas): ?>
			<?php foreach ($tareas_hijas as $tarea_hija): ?>
				<tr data-tarea-id="<?php echo $tarea_hija->getId(); ?>" class="tarea-hija tarea-hijas-container" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
					<td class="text-center">
						<?php
						$tarea = $tarea_hija; // Set for the include
						include __ROOT__ . '/views/admin/ltareas_acciones.php';
						?>
					</td>
					<td class="text-center"><?php echo htmlspecialchars((string)$tarea_hija->getId()); ?></td>
					<td><?php echo $tarea_hija->getNombreProyectoModulo() ? htmlspecialchars($tarea_hija->getNombreProyectoModulo()) : 'N/A'; ?></td>
					<td>
						<div class="descripcion-hija">
							<?php echo htmlspecialchars($tarea_hija->getDescripcion() ?? 'N/A'); ?>
						</div>
					</td>
					<td class="text-center"><span class="badge <?php echo $tarea_hija->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_hija->getNombreTareaEstado()); ?></span></td>
					<td class="text-center"><?php echo format_dateyyymmdd($tarea_hija->getFechaTerminacion()); ?></td>
				</tr>
			<?php endforeach; ?>
		<?php endif; ?>
	<?php endforeach; ?>
<?php else: ?>
	<tr>
		<td colspan="6" class="text-center py-5">
			<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro): ?>
				<i class="fa fa-tasks fa-3x text-muted mb-3"></i>
				<p class="text-muted mb-0">No hay tareas para el proyecto "<?php echo htmlspecialchars($nombre_proyecto_filtro); ?>".</p>
			<?php else: ?>
				<i class="fa fa-folder-open fa-3x text-muted mb-3"></i>
				<p class="text-muted mb-2"><strong>Selecciona un proyecto para ver sus tareas</strong></p>
				<p class="text-muted mb-0">Ve a la <a href="lproyectos" class="text-decoration-none">lista de proyectos</a> y haz clic en el botón "Ver Tareas" del proyecto que deseas gestionar.</p>
			<?php endif; ?>
		</td>
	</tr>
<?php endif; ?>
