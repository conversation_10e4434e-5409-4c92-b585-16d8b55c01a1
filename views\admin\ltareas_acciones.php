<?php
/**
 * Partial view for task actions buttons
 * This file is included in ltareas.view.php for both parent and child tasks
 *
 * Expected variables:
 * @var Tarea $tarea - The task object
 * @var array $tareas_agentes_counts - Array with agent counts per task
 */

use App\classes\Tarea;

// Ensure $tarea is available
if (!isset($tarea)) {
    return;
}
?>

<?php
// Build URL parameters to maintain project context
$url_params = '';
if (isset($filtro_proyecto_id) && $filtro_proyecto_id) {
    $url_params = '&proyecto_id=' . $filtro_proyecto_id;
}
?>

<button type="button" class="btn btn-xs btn-primary me-1 btn-editar-tarea"
        title="Editar Tarea"
        data-bs-toggle="modal"
        data-bs-target="#editarTareaModal"
        data-tareaid="<?php echo $tarea->getId(); ?>"
        data-tareadescripcion="<?php echo htmlspecialchars($tarea->getDescripcion() ?? ''); ?>"
        data-tareaidproyecto="<?php echo $tarea->getIdProyecto(); ?>"
        data-tareaidproyectomodulo="<?php echo $tarea->getIdProyectoModulo(); ?>"
        data-tareanombreproyectomodulo="<?php echo htmlspecialchars($tarea->getNombreProyectoModulo() ?? ''); ?>"
        data-tareaidSprint="<?php echo $tarea->getIdSprint(); ?>">
    <i class="fa fa-edit"></i>
</button>

<?php if ($tarea->getIdTareaPadre() === null): ?>
    <!-- Only show "add child task" button for parent tasks (tasks without a parent) -->
    <a href="itarea?parent_id=<?php echo $tarea->getId(); ?><?php echo $url_params; ?>" class="btn btn-xs btn-success me-1" title="Crear Tarea Hija">
        <i class="fa fa-plus"></i>
    </a>
<?php endif; ?>

<?php
$agentesCount = $tareas_agentes_counts[$tarea->getId()] ?? 0;
$hasAgentes = $agentesCount > 0;
?>
<button type="button"
        class="btn btn-xs me-1 btn-ver-agentes <?php echo $hasAgentes ? 'btn-info' : 'btn-outline-info'; ?>"
        title="<?php echo $hasAgentes ? 'Gestionar Agentes Asociados (' . $agentesCount . ')' : 'Gestionar Agentes de la Tarea'; ?>"
        data-bs-toggle="modal"
        data-bs-target="#gestionarAgentesModal"
        data-tareaid="<?php echo $tarea->getId(); ?>"
        data-tareadescripcion="<?php echo htmlspecialchars($tarea->getDescripcion() ?? ''); ?>">
    <i class="fa fa-users"></i>
</button>

<?php if ($tarea->getIdTareaEstado() !== Tarea::ESTADO_TERMINADO && $tarea->getIdTareaEstado() !== Tarea::ESTADO_ELIMINADO): ?>
    <button type="button" class="btn btn-xs btn-success me-1 btn-marcar-terminada"
            title="Marcar como Terminada"
            data-tareaid="<?php echo $tarea->getId(); ?>">
        <i class="fa fa-check"></i>
    </button>
<?php endif; ?>

<?php if ($tarea->getIdTareaEstado() !== Tarea::ESTADO_EN_PROGRESO && $tarea->getIdTareaEstado() !== Tarea::ESTADO_TERMINADO && $tarea->getIdTareaEstado() !== Tarea::ESTADO_ELIMINADO): ?>
    <button type="button" class="btn btn-xs btn-info me-1 btn-marcar-en-progreso"
            title="Marcar como En Progreso"
            data-tareaid="<?php echo $tarea->getId(); ?>">
        <i class="fa fa-play"></i>
    </button>
<?php endif; ?>

<?php if ($tarea->getIdTareaEstado() !== Tarea::ESTADO_ELIMINADO): ?>
    <button type="button" class="btn btn-xs btn-danger btn-eliminar-tarea"
            title="Eliminar Tarea"
            data-tareaid="<?php echo $tarea->getId(); ?>">
        <i class="fa fa-trash-alt"></i>
    </button>
<?php endif; ?>
